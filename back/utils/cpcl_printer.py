"""
CPCL打印服务
支持通过网络、串口等方式发送CPCL指令到标签打印机
"""

import socket
import serial
import asyncio
import logging
from typing import Optional, Dict, Any
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class PrinterConnectionType(Enum):
    """打印机连接类型"""
    NETWORK = "network"
    SERIAL = "serial"
    USB = "usb"


@dataclass
class PrinterConfig:
    """打印机配置"""
    connection_type: PrinterConnectionType
    host: Optional[str] = None
    port: Optional[int] = None
    serial_port: Optional[str] = None
    baud_rate: int = 9600
    timeout: int = 10


class CPCLPrinter:
    """CPCL打印机类"""
    
    def __init__(self, config: PrinterConfig):
        self.config = config
        self.connection = None
    
    async def connect(self) -> bool:
        """连接到打印机"""
        try:
            if self.config.connection_type == PrinterConnectionType.NETWORK:
                return await self._connect_network()
            elif self.config.connection_type == PrinterConnectionType.SERIAL:
                return self._connect_serial()
            else:
                raise ValueError(f"不支持的连接类型: {self.config.connection_type}")
        except Exception as e:
            logger.error(f"连接打印机失败: {e}")
            return False
    
    async def _connect_network(self) -> bool:
        """网络连接"""
        try:
            self.connection = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.connection.settimeout(self.config.timeout)
            self.connection.connect((self.config.host, self.config.port))
            logger.info(f"已连接到网络打印机: {self.config.host}:{self.config.port}")
            return True
        except Exception as e:
            logger.error(f"网络连接失败: {e}")
            return False
    
    def _connect_serial(self) -> bool:
        """串口连接"""
        try:
            self.connection = serial.Serial(
                port=self.config.serial_port,
                baudrate=self.config.baud_rate,
                timeout=self.config.timeout
            )
            logger.info(f"已连接到串口打印机: {self.config.serial_port}")
            return True
        except Exception as e:
            logger.error(f"串口连接失败: {e}")
            return False
    
    async def send_cpcl(self, cpcl_commands: str) -> bool:
        """发送CPCL指令"""
        if not self.connection:
            logger.error("打印机未连接")
            return False
        
        try:
            if self.config.connection_type == PrinterConnectionType.NETWORK:
                return await self._send_network(cpcl_commands)
            elif self.config.connection_type == PrinterConnectionType.SERIAL:
                return self._send_serial(cpcl_commands)
        except Exception as e:
            logger.error(f"发送CPCL指令失败: {e}")
            return False
    
    async def _send_network(self, commands: str) -> bool:
        """通过网络发送指令"""
        try:
            self.connection.send(commands.encode('utf-8'))
            logger.info("CPCL指令已通过网络发送")
            return True
        except Exception as e:
            logger.error(f"网络发送失败: {e}")
            return False
    
    def _send_serial(self, commands: str) -> bool:
        """通过串口发送指令"""
        try:
            self.connection.write(commands.encode('utf-8'))
            self.connection.flush()
            logger.info("CPCL指令已通过串口发送")
            return True
        except Exception as e:
            logger.error(f"串口发送失败: {e}")
            return False
    
    def disconnect(self):
        """断开连接"""
        if self.connection:
            try:
                self.connection.close()
                logger.info("打印机连接已断开")
            except Exception as e:
                logger.error(f"断开连接失败: {e}")
            finally:
                self.connection = None
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.disconnect()


class CPCLCommandBuilder:
    """CPCL指令构建器"""
    
    def __init__(self):
        self.commands = []
        self.page_width = 200  # mm
        self.page_height = 100  # mm
        self.dpi = 203
    
    def set_page(self, width: int, height: int, dpi: int = 203):
        """设置页面参数"""
        self.page_width = width
        self.page_height = height
        self.dpi = dpi
        
        height_dots = self._mm_to_dots(height)
        width_dots = self._mm_to_dots(width)
        
        self.commands.append(f"! 0 {dpi} {dpi} {height_dots} 1")
        self.commands.append(f"PAGE-WIDTH {width_dots}")
        return self
    
    def _mm_to_dots(self, mm: float) -> int:
        """毫米转换为点数"""
        return round((mm * self.dpi) / 25.4)
    
    def add_text(self, text: str, x: float, y: float, font: int = 4, 
                 rotation: int = 0, bold: bool = False, underline: bool = False):
        """添加文本"""
        x_dots = self._mm_to_dots(x)
        y_dots = self._mm_to_dots(y)
        
        text_cmd = f"TEXT {font} {rotation} {x_dots} {y_dots} {text}"
        
        if bold:
            self.commands.append("SETBOLD 1")
            self.commands.append(text_cmd)
            self.commands.append("SETBOLD 0")
        elif underline:
            self.commands.append("UNDERLINE ON")
            self.commands.append(text_cmd)
            self.commands.append("UNDERLINE OFF")
        else:
            self.commands.append(text_cmd)
        
        return self
    
    def add_barcode(self, data: str, x: float, y: float, barcode_type: str = "CODE128",
                   height: float = 50, width: int = 2, rotation: int = 0, show_text: bool = True):
        """添加条形码"""
        x_dots = self._mm_to_dots(x)
        y_dots = self._mm_to_dots(y)
        height_dots = self._mm_to_dots(height)
        
        barcode_cmd = f"BARCODE {barcode_type} {width} {rotation} {height_dots} {x_dots} {y_dots} {data}"
        self.commands.append(barcode_cmd)
        
        if show_text:
            text_y = y + height + 3
            self.add_text(data, x, text_y, font=2)
        
        return self
    
    def add_qr_code(self, data: str, x: float, y: float, size: int = 6, error_level: str = "M"):
        """添加二维码"""
        x_dots = self._mm_to_dots(x)
        y_dots = self._mm_to_dots(y)
        
        qr_cmd = f"BARCODE QR {x_dots} {y_dots} {error_level} 2 U {size}"
        self.commands.append(qr_cmd)
        self.commands.append(f"MA,{data}")
        self.commands.append("ENDQR")
        
        return self
    
    def add_line(self, x1: float, y1: float, x2: float, y2: float, width: float = 1):
        """添加线条"""
        x1_dots = self._mm_to_dots(x1)
        y1_dots = self._mm_to_dots(y1)
        x2_dots = self._mm_to_dots(x2)
        y2_dots = self._mm_to_dots(y2)
        width_dots = self._mm_to_dots(width)
        
        line_cmd = f"LINE {x1_dots} {y1_dots} {x2_dots} {y2_dots} {width_dots}"
        self.commands.append(line_cmd)
        
        return self
    
    def add_rectangle(self, x: float, y: float, width: float, height: float, 
                     line_width: float = 1, filled: bool = False):
        """添加矩形"""
        x_dots = self._mm_to_dots(x)
        y_dots = self._mm_to_dots(y)
        width_dots = self._mm_to_dots(width)
        height_dots = self._mm_to_dots(height)
        line_width_dots = self._mm_to_dots(line_width)
        
        if filled:
            box_cmd = f"BOX {x_dots} {y_dots} {x_dots + width_dots} {y_dots + height_dots} {line_width_dots}"
            self.commands.append(box_cmd)
        else:
            # 绘制四条边
            self.add_line(x, y, x + width, y, line_width)  # 上边
            self.add_line(x + width, y, x + width, y + height, line_width)  # 右边
            self.add_line(x + width, y + height, x, y + height, line_width)  # 下边
            self.add_line(x, y + height, x, y, line_width)  # 左边
        
        return self
    
    def set_copies(self, copies: int = 1):
        """设置打印份数"""
        self.commands.append(f"PRINT {copies}")
        return self
    
    def build(self) -> str:
        """构建CPCL指令字符串"""
        if not any(cmd.startswith('PRINT') for cmd in self.commands):
            self.commands.append('PRINT')
        
        return '\n'.join(self.commands)
    
    def clear(self):
        """清空指令"""
        self.commands = []
        return self


class CPCLTemplateGenerator:
    """CPCL模板生成器"""
    
    @staticmethod
    def create_product_label(data: Dict[str, Any], width: int = 100, height: int = 60) -> str:
        """创建产品标签"""
        builder = CPCLCommandBuilder()
        builder.set_page(width, height)
        
        # 产品名称
        if data.get('name'):
            builder.add_text(data['name'], 5, 5, font=4, bold=True)
        
        # 产品编号
        if data.get('code'):
            builder.add_text(f"编号: {data['code']}", 5, 15, font=2)
        
        # 价格
        if data.get('price'):
            builder.add_text(f"价格: ¥{data['price']}", 5, 25, font=3)
        
        # 条形码
        if data.get('barcode'):
            builder.add_barcode(data['barcode'], 5, 35, height=15, width=2)
        
        return builder.build()
    
    @staticmethod
    def create_address_label(data: Dict[str, Any], width: int = 100, height: int = 80) -> str:
        """创建地址标签"""
        builder = CPCLCommandBuilder()
        builder.set_page(width, height)
        
        # 收件人
        if data.get('recipient'):
            builder.add_text(f"收件人: {data['recipient']}", 5, 5, font=3, bold=True)
        
        # 电话
        if data.get('phone'):
            builder.add_text(f"电话: {data['phone']}", 5, 18, font=2)
        
        # 地址
        if data.get('address'):
            builder.add_text(f"地址: {data['address']}", 5, 30, font=2)
        
        # 邮编
        if data.get('zip_code'):
            builder.add_text(f"邮编: {data['zip_code']}", 5, 42, font=2)
        
        # 分隔线
        builder.add_line(5, 55, 95, 55, 1)
        
        # 寄件人信息
        if data.get('sender'):
            builder.add_text(f"寄件人: {data['sender']}", 5, 60, font=2)
        
        return builder.build()
    
    @staticmethod
    def create_sample_label(data: Dict[str, Any], width: int = 80, height: int = 50) -> str:
        """创建样品标签"""
        builder = CPCLCommandBuilder()
        builder.set_page(width, height)
        
        # 样品编号
        if data.get('sample_id'):
            builder.add_text(f"样品编号: {data['sample_id']}", 3, 3, font=2, bold=True)
        
        # 采样日期
        if data.get('sample_date'):
            builder.add_text(f"采样日期: {data['sample_date']}", 3, 13, font=1)
        
        # 采样点
        if data.get('sample_point'):
            builder.add_text(f"采样点: {data['sample_point']}", 3, 21, font=1)
        
        # 检测项目
        if data.get('test_items'):
            builder.add_text(f"检测项目: {data['test_items']}", 3, 29, font=1)
        
        # 二维码
        if data.get('qr_code'):
            builder.add_qr_code(data['qr_code'], 55, 10, size=4)
        
        return builder.build()


class CPCLPrintService:
    """CPCL打印服务"""
    
    def __init__(self):
        self.printers = {}  # 打印机连接池
    
    async def add_printer(self, name: str, config: PrinterConfig) -> bool:
        """添加打印机"""
        try:
            printer = CPCLPrinter(config)
            if await printer.connect():
                self.printers[name] = printer
                logger.info(f"打印机 {name} 已添加")
                return True
            else:
                logger.error(f"打印机 {name} 连接失败")
                return False
        except Exception as e:
            logger.error(f"添加打印机 {name} 失败: {e}")
            return False
    
    async def print_label(self, printer_name: str, cpcl_commands: str) -> bool:
        """打印标签"""
        if printer_name not in self.printers:
            logger.error(f"打印机 {printer_name} 不存在")
            return False
        
        printer = self.printers[printer_name]
        return await printer.send_cpcl(cpcl_commands)
    
    async def print_template(self, printer_name: str, template_type: str, 
                           data: Dict[str, Any], **kwargs) -> bool:
        """使用模板打印"""
        if template_type == 'product':
            cpcl_commands = CPCLTemplateGenerator.create_product_label(data, **kwargs)
        elif template_type == 'address':
            cpcl_commands = CPCLTemplateGenerator.create_address_label(data, **kwargs)
        elif template_type == 'sample':
            cpcl_commands = CPCLTemplateGenerator.create_sample_label(data, **kwargs)
        else:
            logger.error(f"不支持的模板类型: {template_type}")
            return False
        
        return await self.print_label(printer_name, cpcl_commands)
    
    def remove_printer(self, name: str):
        """移除打印机"""
        if name in self.printers:
            self.printers[name].disconnect()
            del self.printers[name]
            logger.info(f"打印机 {name} 已移除")
    
    def get_printer_status(self, name: str) -> Dict[str, Any]:
        """获取打印机状态"""
        if name not in self.printers:
            return {"status": "not_found"}
        
        printer = self.printers[name]
        return {
            "status": "connected" if printer.connection else "disconnected",
            "config": {
                "connection_type": printer.config.connection_type.value,
                "host": printer.config.host,
                "port": printer.config.port,
                "serial_port": printer.config.serial_port
            }
        }
    
    def list_printers(self) -> Dict[str, Dict[str, Any]]:
        """列出所有打印机"""
        return {name: self.get_printer_status(name) for name in self.printers}
    
    def cleanup(self):
        """清理资源"""
        for printer in self.printers.values():
            printer.disconnect()
        self.printers.clear()


# 全局打印服务实例
print_service = CPCLPrintService()
