"""
CPCL打印API接口
提供标签打印相关的API服务
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from enum import Enum

from utils.cpcl_printer import (
    CPCLPrintService, 
    PrinterConfig, 
    PrinterConnectionType,
    CPCLTemplateGenerator,
    print_service
)
from utils.response import success, fail
from utils.log import logger

router = APIRouter(prefix="/cpcl", tags=["CPCL打印"])


class ConnectionTypeEnum(str, Enum):
    """连接类型枚举"""
    NETWORK = "network"
    SERIAL = "serial"
    USB = "usb"


class PrinterConfigModel(BaseModel):
    """打印机配置模型"""
    connection_type: ConnectionTypeEnum
    host: Optional[str] = None
    port: Optional[int] = None
    serial_port: Optional[str] = None
    baud_rate: int = 9600
    timeout: int = 10


class PrinterAddRequest(BaseModel):
    """添加打印机请求"""
    name: str = Field(..., description="打印机名称")
    config: PrinterConfigModel = Field(..., description="打印机配置")


class PrintRequest(BaseModel):
    """打印请求"""
    printer_name: str = Field(..., description="打印机名称")
    cpcl_commands: str = Field(..., description="CPCL指令")


class TemplatePrintRequest(BaseModel):
    """模板打印请求"""
    printer_name: str = Field(..., description="打印机名称")
    template_type: str = Field(..., description="模板类型", regex="^(product|address|sample)$")
    data: Dict[str, Any] = Field(..., description="标签数据")
    width: Optional[int] = Field(100, description="标签宽度(mm)")
    height: Optional[int] = Field(60, description="标签高度(mm)")


class ProductLabelData(BaseModel):
    """产品标签数据"""
    name: Optional[str] = None
    code: Optional[str] = None
    price: Optional[float] = None
    barcode: Optional[str] = None


class AddressLabelData(BaseModel):
    """地址标签数据"""
    recipient: Optional[str] = None
    phone: Optional[str] = None
    address: Optional[str] = None
    zip_code: Optional[str] = None
    sender: Optional[str] = None


class SampleLabelData(BaseModel):
    """样品标签数据"""
    sample_id: Optional[str] = None
    sample_date: Optional[str] = None
    sample_point: Optional[str] = None
    test_items: Optional[str] = None
    qr_code: Optional[str] = None


@router.post("/printer/add", summary="添加打印机")
async def add_printer(request: PrinterAddRequest):
    """添加打印机配置"""
    try:
        # 转换连接类型
        connection_type = PrinterConnectionType(request.config.connection_type.value)
        
        # 创建打印机配置
        config = PrinterConfig(
            connection_type=connection_type,
            host=request.config.host,
            port=request.config.port,
            serial_port=request.config.serial_port,
            baud_rate=request.config.baud_rate,
            timeout=request.config.timeout
        )
        
        # 添加打印机
        result = await print_service.add_printer(request.name, config)
        
        if result:
            logger.info(f"打印机 {request.name} 添加成功")
            return success(msg=f"打印机 {request.name} 添加成功")
        else:
            logger.error(f"打印机 {request.name} 添加失败")
            return fail(msg=f"打印机 {request.name} 添加失败")
            
    except Exception as e:
        logger.error(f"添加打印机异常: {e}")
        raise HTTPException(status_code=500, detail=f"添加打印机失败: {str(e)}")


@router.delete("/printer/{printer_name}", summary="删除打印机")
async def remove_printer(printer_name: str):
    """删除打印机"""
    try:
        print_service.remove_printer(printer_name)
        logger.info(f"打印机 {printer_name} 删除成功")
        return success(msg=f"打印机 {printer_name} 删除成功")
        
    except Exception as e:
        logger.error(f"删除打印机异常: {e}")
        raise HTTPException(status_code=500, detail=f"删除打印机失败: {str(e)}")


@router.get("/printer/list", summary="获取打印机列表")
async def list_printers():
    """获取所有打印机列表"""
    try:
        printers = print_service.list_printers()
        return success(data=printers, msg="获取打印机列表成功")
        
    except Exception as e:
        logger.error(f"获取打印机列表异常: {e}")
        raise HTTPException(status_code=500, detail=f"获取打印机列表失败: {str(e)}")


@router.get("/printer/{printer_name}/status", summary="获取打印机状态")
async def get_printer_status(printer_name: str):
    """获取指定打印机状态"""
    try:
        status = print_service.get_printer_status(printer_name)
        return success(data=status, msg="获取打印机状态成功")
        
    except Exception as e:
        logger.error(f"获取打印机状态异常: {e}")
        raise HTTPException(status_code=500, detail=f"获取打印机状态失败: {str(e)}")


@router.post("/print", summary="打印CPCL指令")
async def print_cpcl(request: PrintRequest):
    """发送CPCL指令到打印机"""
    try:
        result = await print_service.print_label(request.printer_name, request.cpcl_commands)
        
        if result:
            logger.info(f"CPCL指令发送成功，打印机: {request.printer_name}")
            return success(msg="打印任务发送成功")
        else:
            logger.error(f"CPCL指令发送失败，打印机: {request.printer_name}")
            return fail(msg="打印任务发送失败")
            
    except Exception as e:
        logger.error(f"打印CPCL指令异常: {e}")
        raise HTTPException(status_code=500, detail=f"打印失败: {str(e)}")


@router.post("/print/template", summary="使用模板打印")
async def print_template(request: TemplatePrintRequest):
    """使用预定义模板打印标签"""
    try:
        result = await print_service.print_template(
            request.printer_name,
            request.template_type,
            request.data,
            width=request.width,
            height=request.height
        )
        
        if result:
            logger.info(f"模板打印成功，打印机: {request.printer_name}, 模板: {request.template_type}")
            return success(msg="模板打印成功")
        else:
            logger.error(f"模板打印失败，打印机: {request.printer_name}, 模板: {request.template_type}")
            return fail(msg="模板打印失败")
            
    except Exception as e:
        logger.error(f"模板打印异常: {e}")
        raise HTTPException(status_code=500, detail=f"模板打印失败: {str(e)}")


@router.post("/print/product", summary="打印产品标签")
async def print_product_label(
    printer_name: str,
    data: ProductLabelData,
    width: int = 100,
    height: int = 60
):
    """打印产品标签"""
    try:
        label_data = data.dict(exclude_none=True)
        cpcl_commands = CPCLTemplateGenerator.create_product_label(
            label_data, width=width, height=height
        )
        
        result = await print_service.print_label(printer_name, cpcl_commands)
        
        if result:
            logger.info(f"产品标签打印成功，打印机: {printer_name}")
            return success(data={"cpcl_commands": cpcl_commands}, msg="产品标签打印成功")
        else:
            logger.error(f"产品标签打印失败，打印机: {printer_name}")
            return fail(msg="产品标签打印失败")
            
    except Exception as e:
        logger.error(f"产品标签打印异常: {e}")
        raise HTTPException(status_code=500, detail=f"产品标签打印失败: {str(e)}")


@router.post("/print/address", summary="打印地址标签")
async def print_address_label(
    printer_name: str,
    data: AddressLabelData,
    width: int = 100,
    height: int = 80
):
    """打印地址标签"""
    try:
        label_data = data.dict(exclude_none=True)
        cpcl_commands = CPCLTemplateGenerator.create_address_label(
            label_data, width=width, height=height
        )
        
        result = await print_service.print_label(printer_name, cpcl_commands)
        
        if result:
            logger.info(f"地址标签打印成功，打印机: {printer_name}")
            return success(data={"cpcl_commands": cpcl_commands}, msg="地址标签打印成功")
        else:
            logger.error(f"地址标签打印失败，打印机: {printer_name}")
            return fail(msg="地址标签打印失败")
            
    except Exception as e:
        logger.error(f"地址标签打印异常: {e}")
        raise HTTPException(status_code=500, detail=f"地址标签打印失败: {str(e)}")


@router.post("/print/sample", summary="打印样品标签")
async def print_sample_label(
    printer_name: str,
    data: SampleLabelData,
    width: int = 80,
    height: int = 50
):
    """打印样品标签"""
    try:
        label_data = data.dict(exclude_none=True)
        cpcl_commands = CPCLTemplateGenerator.create_sample_label(
            label_data, width=width, height=height
        )
        
        result = await print_service.print_label(printer_name, cpcl_commands)
        
        if result:
            logger.info(f"样品标签打印成功，打印机: {printer_name}")
            return success(data={"cpcl_commands": cpcl_commands}, msg="样品标签打印成功")
        else:
            logger.error(f"样品标签打印失败，打印机: {printer_name}")
            return fail(msg="样品标签打印失败")
            
    except Exception as e:
        logger.error(f"样品标签打印异常: {e}")
        raise HTTPException(status_code=500, detail=f"样品标签打印失败: {str(e)}")


@router.post("/generate/cpcl", summary="生成CPCL指令")
async def generate_cpcl(request: TemplatePrintRequest):
    """生成CPCL指令（不打印）"""
    try:
        if request.template_type == 'product':
            cpcl_commands = CPCLTemplateGenerator.create_product_label(
                request.data, width=request.width, height=request.height
            )
        elif request.template_type == 'address':
            cpcl_commands = CPCLTemplateGenerator.create_address_label(
                request.data, width=request.width, height=request.height
            )
        elif request.template_type == 'sample':
            cpcl_commands = CPCLTemplateGenerator.create_sample_label(
                request.data, width=request.width, height=request.height
            )
        else:
            return fail(msg=f"不支持的模板类型: {request.template_type}")
        
        return success(data={"cpcl_commands": cpcl_commands}, msg="CPCL指令生成成功")
        
    except Exception as e:
        logger.error(f"生成CPCL指令异常: {e}")
        raise HTTPException(status_code=500, detail=f"生成CPCL指令失败: {str(e)}")


@router.post("/test/connection", summary="测试打印机连接")
async def test_printer_connection(request: PrinterConfigModel):
    """测试打印机连接"""
    try:
        # 转换连接类型
        connection_type = PrinterConnectionType(request.connection_type.value)
        
        # 创建临时打印机配置
        config = PrinterConfig(
            connection_type=connection_type,
            host=request.host,
            port=request.port,
            serial_port=request.serial_port,
            baud_rate=request.baud_rate,
            timeout=request.timeout
        )
        
        # 创建临时打印机实例进行测试
        from utils.cpcl_printer import CPCLPrinter
        
        async with CPCLPrinter(config) as printer:
            if await printer.connect():
                # 发送测试指令
                test_commands = f"! 0 203 203 100 1\nTEXT 4 0 50 50 CONNECTION TEST\nPRINT\n"
                result = await printer.send_cpcl(test_commands)
                
                if result:
                    return success(msg="打印机连接测试成功")
                else:
                    return fail(msg="打印机连接成功，但发送测试指令失败")
            else:
                return fail(msg="打印机连接失败")
                
    except Exception as e:
        logger.error(f"测试打印机连接异常: {e}")
        return fail(msg=f"连接测试失败: {str(e)}")


# 应用启动时的初始化
@router.on_event("startup")
async def startup_event():
    """应用启动时初始化默认打印机"""
    try:
        # 可以在这里添加默认打印机配置
        logger.info("CPCL打印服务初始化完成")
    except Exception as e:
        logger.error(f"CPCL打印服务初始化失败: {e}")


# 应用关闭时的清理
@router.on_event("shutdown")
async def shutdown_event():
    """应用关闭时清理资源"""
    try:
        print_service.cleanup()
        logger.info("CPCL打印服务清理完成")
    except Exception as e:
        logger.error(f"CPCL打印服务清理失败: {e}")
