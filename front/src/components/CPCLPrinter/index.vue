<template>
  <div class="cpcl-printer">
    <el-card class="printer-card">
      <template #header>
        <div class="card-header">
          <span>CPCL标签打印</span>
          <el-button 
            type="primary" 
            size="small" 
            @click="showSettings = true"
            :icon="Setting"
          >
            打印设置
          </el-button>
        </div>
      </template>
      
      <!-- 打印预览区域 -->
      <div class="preview-area">
        <div class="preview-label" :style="previewStyle">
          <div class="preview-content" v-html="previewHtml"></div>
        </div>
      </div>
      
      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-space>
          <el-button 
            type="primary" 
            @click="handlePrint"
            :loading="printing"
            :icon="Printer"
          >
            打印标签
          </el-button>
          <el-button @click="previewCPCL">预览指令</el-button>
          <el-button @click="exportCPCL">导出指令</el-button>
          <el-button @click="clearContent">清空内容</el-button>
        </el-space>
      </div>
    </el-card>
    
    <!-- 打印设置对话框 -->
    <el-dialog
      v-model="showSettings"
      title="打印机设置"
      width="600px"
    >
      <el-form :model="printerSettings" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="标签宽度">
              <el-input-number 
                v-model="printerSettings.width" 
                :min="10" 
                :max="200"
                controls-position="right"
              /> mm
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="标签高度">
              <el-input-number 
                v-model="printerSettings.height" 
                :min="10" 
                :max="200"
                controls-position="right"
              /> mm
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="打印分辨率">
              <el-select v-model="printerSettings.dpi">
                <el-option label="203 DPI" :value="203" />
                <el-option label="300 DPI" :value="300" />
                <el-option label="600 DPI" :value="600" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="打印份数">
              <el-input-number 
                v-model="printerSettings.copies" 
                :min="1" 
                :max="100"
                controls-position="right"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="连接方式">
          <el-radio-group v-model="printerSettings.method">
            <el-radio label="websocket">WebSocket</el-radio>
            <el-radio label="http">HTTP</el-radio>
            <el-radio label="serial">串口</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-row :gutter="20" v-if="printerSettings.method !== 'serial'">
          <el-col :span="12">
            <el-form-item label="主机地址">
              <el-input v-model="printerSettings.host" placeholder="localhost" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="端口">
              <el-input-number 
                v-model="printerSettings.port" 
                :min="1" 
                :max="65535"
                controls-position="right"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="打印机名称">
          <el-input v-model="printerSettings.printerName" placeholder="默认打印机" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showSettings = false">取消</el-button>
          <el-button type="primary" @click="saveSettings">保存设置</el-button>
          <el-button type="success" @click="testConnection">测试连接</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- CPCL指令预览对话框 -->
    <el-dialog
      v-model="showCPCLPreview"
      title="CPCL指令预览"
      width="800px"
    >
      <el-input
        v-model="cpclCommands"
        type="textarea"
        :rows="20"
        readonly
        style="font-family: monospace;"
      />
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showCPCLPreview = false">关闭</el-button>
          <el-button type="primary" @click="copyCPCL">复制指令</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Setting, Printer } from '@element-plus/icons-vue'
import { CPCLBuilder, sendToPrinter } from '@/utils/cpcl'

// Props
const props = defineProps({
  // 标签数据
  labelData: {
    type: Object,
    default: () => ({})
  },
  // 标签类型
  labelType: {
    type: String,
    default: 'custom',
    validator: (value) => ['product', 'address', 'sample', 'custom'].includes(value)
  },
  // 自定义CPCL指令
  customCPCL: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['print-success', 'print-error', 'settings-changed'])

// 响应式数据
const printing = ref(false)
const showSettings = ref(false)
const showCPCLPreview = ref(false)
const cpclCommands = ref('')

const printerSettings = ref({
  width: 100,        // 标签宽度(mm)
  height: 60,        // 标签高度(mm)
  dpi: 203,          // 打印分辨率
  copies: 1,         // 打印份数
  method: 'websocket', // 连接方式
  host: 'localhost', // 主机地址
  port: 9100,        // 端口
  printerName: 'default' // 打印机名称
})

// 计算属性
const previewStyle = computed(() => ({
  width: `${printerSettings.value.width * 2}px`,
  height: `${printerSettings.value.height * 2}px`,
  border: '2px solid #ddd',
  backgroundColor: 'white',
  position: 'relative',
  margin: '20px auto',
  transform: 'scale(0.8)',
  transformOrigin: 'center'
}))

const previewHtml = computed(() => {
  // 根据标签类型和数据生成预览HTML
  if (props.labelType === 'product') {
    return generateProductPreview()
  } else if (props.labelType === 'address') {
    return generateAddressPreview()
  } else if (props.labelType === 'sample') {
    return generateSamplePreview()
  } else {
    return generateCustomPreview()
  }
})

// 方法
const generateProductPreview = () => {
  const data = props.labelData
  return `
    <div style="padding: 10px; font-family: Arial, sans-serif;">
      <div style="font-size: 16px; font-weight: bold; margin-bottom: 8px;">
        ${data.name || '产品名称'}
      </div>
      <div style="font-size: 12px; margin-bottom: 5px;">
        编号: ${data.code || 'P001'}
      </div>
      <div style="font-size: 14px; margin-bottom: 10px;">
        价格: ¥${data.price || '0.00'}
      </div>
      <div style="border: 1px solid #000; padding: 5px; text-align: center; font-family: monospace;">
        ${data.barcode || '1234567890'}
      </div>
    </div>
  `
}

const generateAddressPreview = () => {
  const data = props.labelData
  return `
    <div style="padding: 10px; font-family: Arial, sans-serif;">
      <div style="font-size: 14px; font-weight: bold; margin-bottom: 8px;">
        收件人: ${data.recipient || '张三'}
      </div>
      <div style="font-size: 12px; margin-bottom: 5px;">
        电话: ${data.phone || '13800138000'}
      </div>
      <div style="font-size: 12px; margin-bottom: 5px;">
        地址: ${data.address || '北京市朝阳区'}
      </div>
      <div style="font-size: 12px; margin-bottom: 10px;">
        邮编: ${data.zipCode || '100000'}
      </div>
      <hr style="border: 1px solid #000; margin: 10px 0;">
      <div style="font-size: 12px;">
        寄件人: ${data.sender || '李四'}
      </div>
    </div>
  `
}

const generateSamplePreview = () => {
  const data = props.labelData
  return `
    <div style="padding: 8px; font-family: Arial, sans-serif; position: relative;">
      <div style="font-size: 12px; font-weight: bold; margin-bottom: 5px;">
        样品编号: ${data.sampleId || 'S001'}
      </div>
      <div style="font-size: 10px; margin-bottom: 3px;">
        采样日期: ${data.sampleDate || '2024-01-01'}
      </div>
      <div style="font-size: 10px; margin-bottom: 3px;">
        采样点: ${data.samplePoint || 'A区1号'}
      </div>
      <div style="font-size: 10px; margin-bottom: 5px;">
        检测项目: ${data.testItems || 'pH值'}
      </div>
      <div style="position: absolute; right: 8px; top: 8px; width: 40px; height: 40px; border: 1px solid #000; display: flex; align-items: center; justify-content: center; font-size: 8px;">
        QR
      </div>
    </div>
  `
}

const generateCustomPreview = () => {
  return `
    <div style="padding: 10px; font-family: Arial, sans-serif; text-align: center;">
      <div style="font-size: 14px; margin-bottom: 10px;">
        自定义标签
      </div>
      <div style="font-size: 12px; color: #666;">
        请在设置中配置标签内容
      </div>
    </div>
  `
}

const generateCPCLCommands = () => {
  const builder = new CPCLBuilder()
  builder.setPage(
    printerSettings.value.width,
    printerSettings.value.height,
    printerSettings.value.dpi
  )
  
  if (props.customCPCL) {
    // 使用自定义CPCL指令
    return props.customCPCL
  }
  
  // 根据标签类型生成CPCL指令
  if (props.labelType === 'product') {
    return generateProductCPCL(builder)
  } else if (props.labelType === 'address') {
    return generateAddressCPCL(builder)
  } else if (props.labelType === 'sample') {
    return generateSampleCPCL(builder)
  }
  
  return builder.build()
}

const generateProductCPCL = (builder) => {
  const data = props.labelData
  
  // 产品名称
  if (data.name) {
    builder.addText(data.name, 5, 5, { font: 4, bold: true })
  }
  
  // 产品编号
  if (data.code) {
    builder.addText(`编号: ${data.code}`, 5, 15, { font: 2 })
  }
  
  // 价格
  if (data.price) {
    builder.addText(`价格: ¥${data.price}`, 5, 25, { font: 3 })
  }
  
  // 条形码
  if (data.barcode) {
    builder.addBarcode(data.barcode, 5, 35, {
      type: 'CODE128',
      height: 15,
      width: 2,
      showText: true
    })
  }
  
  builder.setCopies(printerSettings.value.copies)
  return builder.build()
}

const generateAddressCPCL = (builder) => {
  const data = props.labelData
  
  // 收件人
  if (data.recipient) {
    builder.addText(`收件人: ${data.recipient}`, 5, 5, { font: 3, bold: true })
  }
  
  // 电话
  if (data.phone) {
    builder.addText(`电话: ${data.phone}`, 5, 18, { font: 2 })
  }
  
  // 地址
  if (data.address) {
    builder.addText(`地址: ${data.address}`, 5, 30, { font: 2 })
  }
  
  // 邮编
  if (data.zipCode) {
    builder.addText(`邮编: ${data.zipCode}`, 5, 42, { font: 2 })
  }
  
  // 分隔线
  builder.addLine(5, 55, 95, 55, 1)
  
  // 寄件人信息
  if (data.sender) {
    builder.addText(`寄件人: ${data.sender}`, 5, 60, { font: 2 })
  }
  
  builder.setCopies(printerSettings.value.copies)
  return builder.build()
}

const generateSampleCPCL = (builder) => {
  const data = props.labelData
  
  // 样品编号
  if (data.sampleId) {
    builder.addText(`样品编号: ${data.sampleId}`, 3, 3, { font: 2, bold: true })
  }
  
  // 采样日期
  if (data.sampleDate) {
    builder.addText(`采样日期: ${data.sampleDate}`, 3, 13, { font: 1 })
  }
  
  // 采样点
  if (data.samplePoint) {
    builder.addText(`采样点: ${data.samplePoint}`, 3, 21, { font: 1 })
  }
  
  // 检测项目
  if (data.testItems) {
    builder.addText(`检测项目: ${data.testItems}`, 3, 29, { font: 1 })
  }
  
  // 二维码
  if (data.qrCode) {
    builder.addQRCode(data.qrCode, 55, 10, { size: 4 })
  }
  
  builder.setCopies(printerSettings.value.copies)
  return builder.build()
}

const handlePrint = async () => {
  try {
    printing.value = true
    
    // 生成CPCL指令
    const commands = generateCPCLCommands()
    cpclCommands.value = commands
    
    // 发送到打印机
    await sendToPrinter(commands, printerSettings.value)
    
    ElMessage.success('打印任务已发送')
    emit('print-success', { commands, settings: printerSettings.value })
    
  } catch (error) {
    console.error('打印失败:', error)
    ElMessage.error('打印失败: ' + error.message)
    emit('print-error', error)
    
  } finally {
    printing.value = false
  }
}

const previewCPCL = () => {
  cpclCommands.value = generateCPCLCommands()
  showCPCLPreview.value = true
}

const exportCPCL = () => {
  const commands = generateCPCLCommands()
  const blob = new Blob([commands], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = 'label.cpcl'
  a.click()
  URL.revokeObjectURL(url)
  
  ElMessage.success('CPCL指令已导出')
}

const clearContent = () => {
  ElMessageBox.confirm('确定要清空标签内容吗？', '确认', {
    type: 'warning'
  }).then(() => {
    emit('clear-content')
    ElMessage.success('内容已清空')
  }).catch(() => {})
}

const saveSettings = () => {
  // 保存设置到本地存储
  localStorage.setItem('cpcl-printer-settings', JSON.stringify(printerSettings.value))
  showSettings.value = false
  emit('settings-changed', printerSettings.value)
  ElMessage.success('设置已保存')
}

const testConnection = async () => {
  try {
    // 发送测试指令
    const testCommands = `! 0 ${printerSettings.value.dpi} ${printerSettings.value.dpi} 100 1\nTEXT 4 0 50 50 TEST\nPRINT\n`
    await sendToPrinter(testCommands, printerSettings.value)
    ElMessage.success('连接测试成功')
  } catch (error) {
    ElMessage.error('连接测试失败: ' + error.message)
  }
}

const copyCPCL = () => {
  navigator.clipboard.writeText(cpclCommands.value).then(() => {
    ElMessage.success('CPCL指令已复制到剪贴板')
  }).catch(() => {
    ElMessage.error('复制失败')
  })
}

// 监听设置变化
watch(() => printerSettings.value, () => {
  // 设置变化时更新预览
}, { deep: true })

// 初始化
const init = () => {
  // 从本地存储加载设置
  const savedSettings = localStorage.getItem('cpcl-printer-settings')
  if (savedSettings) {
    try {
      printerSettings.value = { ...printerSettings.value, ...JSON.parse(savedSettings) }
    } catch (error) {
      console.error('加载设置失败:', error)
    }
  }
}

init()
</script>

<style scoped>
.cpcl-printer {
  max-width: 800px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.preview-area {
  text-align: center;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 4px;
  margin-bottom: 20px;
}

.preview-label {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.preview-content {
  width: 100%;
  height: 100%;
}

.action-buttons {
  text-align: center;
  padding: 20px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
