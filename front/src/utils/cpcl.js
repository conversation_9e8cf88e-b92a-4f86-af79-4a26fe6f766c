/**
 * CPCL (Common Printing Command Language) 指令生成工具
 * 用于生成标签打印机的CPCL指令
 */

/**
 * CPCL指令构建器类
 */
export class CPCLBuilder {
  constructor() {
    this.commands = []
    this.pageWidth = 200  // 默认页面宽度(mm)
    this.pageHeight = 100 // 默认页面高度(mm)
    this.dpi = 203        // 默认DPI
  }

  /**
   * 设置页面参数
   * @param {number} width - 页面宽度(mm)
   * @param {number} height - 页面高度(mm) 
   * @param {number} dpi - 打印分辨率
   */
  setPage(width, height, dpi = 203) {
    this.pageWidth = width
    this.pageHeight = height
    this.dpi = dpi
    
    // CPCL页面设置指令
    this.commands.push(`! 0 ${dpi} ${dpi} ${this.mmToDots(height)} 1`)
    this.commands.push(`PAGE-WIDTH ${this.mmToDots(width)}`)
    return this
  }

  /**
   * 毫米转换为点数
   * @param {number} mm - 毫米值
   * @returns {number} 点数
   */
  mmToDots(mm) {
    return Math.round((mm * this.dpi) / 25.4)
  }

  /**
   * 添加文本
   * @param {string} text - 文本内容
   * @param {number} x - X坐标(mm)
   * @param {number} y - Y坐标(mm)
   * @param {Object} options - 选项
   */
  addText(text, x, y, options = {}) {
    const defaults = {
      font: 4,        // 字体大小 (0-7)
      rotation: 0,    // 旋转角度 (0, 90, 180, 270)
      bold: false,    // 是否加粗
      underline: false // 是否下划线
    }
    
    const opts = { ...defaults, ...options }
    
    // 转换坐标为点数
    const xDots = this.mmToDots(x)
    const yDots = this.mmToDots(y)
    
    // 构建文本指令
    let textCmd = `TEXT ${opts.font} ${opts.rotation} ${xDots} ${yDots} ${text}`
    
    if (opts.bold) {
      textCmd = `SETBOLD 1\n${textCmd}\nSETBOLD 0`
    }
    
    if (opts.underline) {
      textCmd = `UNDERLINE ON\n${textCmd}\nUNDERLINE OFF`
    }
    
    this.commands.push(textCmd)
    return this
  }

  /**
   * 添加条形码
   * @param {string} data - 条形码数据
   * @param {number} x - X坐标(mm)
   * @param {number} y - Y坐标(mm)
   * @param {Object} options - 选项
   */
  addBarcode(data, x, y, options = {}) {
    const defaults = {
      type: 'CODE128',  // 条形码类型
      height: 50,       // 高度(mm)
      width: 2,         // 宽度倍数
      rotation: 0,      // 旋转角度
      showText: true    // 是否显示文本
    }
    
    const opts = { ...defaults, ...options }
    
    const xDots = this.mmToDots(x)
    const yDots = this.mmToDots(y)
    const heightDots = this.mmToDots(opts.height)
    
    // 构建条形码指令
    let barcodeCmd = `BARCODE ${opts.type} ${opts.width} ${opts.rotation} ${heightDots} ${xDots} ${yDots} ${data}`
    
    this.commands.push(barcodeCmd)
    
    // 添加条形码文本
    if (opts.showText) {
      const textY = y + opts.height + 3
      this.addText(data, x, textY, { font: 2 })
    }
    
    return this
  }

  /**
   * 添加二维码
   * @param {string} data - 二维码数据
   * @param {number} x - X坐标(mm)
   * @param {number} y - Y坐标(mm)
   * @param {Object} options - 选项
   */
  addQRCode(data, x, y, options = {}) {
    const defaults = {
      size: 6,        // 二维码大小 (1-10)
      errorLevel: 'M' // 错误纠正级别 (L, M, Q, H)
    }
    
    const opts = { ...defaults, ...options }
    
    const xDots = this.mmToDots(x)
    const yDots = this.mmToDots(y)
    
    // 构建二维码指令
    const qrCmd = `BARCODE QR ${xDots} ${yDots} M 2 U ${opts.size}\nMA,${data}\nENDQR`
    
    this.commands.push(qrCmd)
    return this
  }

  /**
   * 添加线条
   * @param {number} x1 - 起始X坐标(mm)
   * @param {number} y1 - 起始Y坐标(mm)
   * @param {number} x2 - 结束X坐标(mm)
   * @param {number} y2 - 结束Y坐标(mm)
   * @param {number} width - 线条宽度(mm)
   */
  addLine(x1, y1, x2, y2, width = 1) {
    const x1Dots = this.mmToDots(x1)
    const y1Dots = this.mmToDots(y1)
    const x2Dots = this.mmToDots(x2)
    const y2Dots = this.mmToDots(y2)
    const widthDots = this.mmToDots(width)
    
    const lineCmd = `LINE ${x1Dots} ${y1Dots} ${x2Dots} ${y2Dots} ${widthDots}`
    this.commands.push(lineCmd)
    return this
  }

  /**
   * 添加矩形
   * @param {number} x - X坐标(mm)
   * @param {number} y - Y坐标(mm)
   * @param {number} width - 宽度(mm)
   * @param {number} height - 高度(mm)
   * @param {number} lineWidth - 线条宽度(mm)
   * @param {boolean} filled - 是否填充
   */
  addRectangle(x, y, width, height, lineWidth = 1, filled = false) {
    const xDots = this.mmToDots(x)
    const yDots = this.mmToDots(y)
    const widthDots = this.mmToDots(width)
    const heightDots = this.mmToDots(height)
    const lineWidthDots = this.mmToDots(lineWidth)
    
    if (filled) {
      const boxCmd = `BOX ${xDots} ${yDots} ${xDots + widthDots} ${yDots + heightDots} ${lineWidthDots}`
      this.commands.push(boxCmd)
    } else {
      // 绘制四条边
      this.addLine(x, y, x + width, y, lineWidth)                    // 上边
      this.addLine(x + width, y, x + width, y + height, lineWidth)   // 右边
      this.addLine(x + width, y + height, x, y + height, lineWidth)  // 下边
      this.addLine(x, y + height, x, y, lineWidth)                   // 左边
    }
    
    return this
  }

  /**
   * 添加图片
   * @param {string} imageData - 图片数据(Base64或路径)
   * @param {number} x - X坐标(mm)
   * @param {number} y - Y坐标(mm)
   * @param {Object} options - 选项
   */
  addImage(imageData, x, y, options = {}) {
    const defaults = {
      width: 50,    // 图片宽度(mm)
      height: 50,   // 图片高度(mm)
      dithering: 0  // 抖动算法
    }
    
    const opts = { ...defaults, ...options }
    
    const xDots = this.mmToDots(x)
    const yDots = this.mmToDots(y)
    const widthDots = this.mmToDots(opts.width)
    const heightDots = this.mmToDots(opts.height)
    
    // 注意：实际图片数据需要转换为CPCL支持的格式
    const imageCmd = `BITMAP ${xDots} ${yDots} ${widthDots} ${heightDots} ${opts.dithering} ${imageData}`
    this.commands.push(imageCmd)
    return this
  }

  /**
   * 设置打印份数
   * @param {number} copies - 打印份数
   */
  setCopies(copies = 1) {
    this.commands.push(`PRINT ${copies}`)
    return this
  }

  /**
   * 生成CPCL指令字符串
   * @returns {string} CPCL指令
   */
  build() {
    // 添加结束指令
    if (!this.commands.some(cmd => cmd.startsWith('PRINT'))) {
      this.commands.push('PRINT')
    }
    
    return this.commands.join('\n')
  }

  /**
   * 清空指令
   */
  clear() {
    this.commands = []
    return this
  }
}

/**
 * 创建标准标签模板
 */
export class CPCLTemplates {
  /**
   * 创建产品标签
   * @param {Object} data - 产品数据
   * @param {Object} options - 选项
   */
  static createProductLabel(data, options = {}) {
    const defaults = {
      width: 100,   // 标签宽度(mm)
      height: 60,   // 标签高度(mm)
      dpi: 203      // 打印分辨率
    }
    
    const opts = { ...defaults, ...options }
    const builder = new CPCLBuilder()
    
    builder.setPage(opts.width, opts.height, opts.dpi)
    
    // 产品名称
    if (data.name) {
      builder.addText(data.name, 5, 5, { font: 4, bold: true })
    }
    
    // 产品编号
    if (data.code) {
      builder.addText(`编号: ${data.code}`, 5, 15, { font: 2 })
    }
    
    // 价格
    if (data.price) {
      builder.addText(`价格: ¥${data.price}`, 5, 25, { font: 3 })
    }
    
    // 条形码
    if (data.barcode) {
      builder.addBarcode(data.barcode, 5, 35, {
        type: 'CODE128',
        height: 15,
        width: 2,
        showText: true
      })
    }
    
    return builder.build()
  }

  /**
   * 创建地址标签
   * @param {Object} data - 地址数据
   * @param {Object} options - 选项
   */
  static createAddressLabel(data, options = {}) {
    const defaults = {
      width: 100,
      height: 80,
      dpi: 203
    }
    
    const opts = { ...defaults, ...options }
    const builder = new CPCLBuilder()
    
    builder.setPage(opts.width, opts.height, opts.dpi)
    
    // 收件人
    if (data.recipient) {
      builder.addText(`收件人: ${data.recipient}`, 5, 5, { font: 3, bold: true })
    }
    
    // 电话
    if (data.phone) {
      builder.addText(`电话: ${data.phone}`, 5, 18, { font: 2 })
    }
    
    // 地址
    if (data.address) {
      builder.addText(`地址: ${data.address}`, 5, 30, { font: 2 })
    }
    
    // 邮编
    if (data.zipCode) {
      builder.addText(`邮编: ${data.zipCode}`, 5, 42, { font: 2 })
    }
    
    // 分隔线
    builder.addLine(5, 55, 95, 55, 1)
    
    // 寄件人信息
    if (data.sender) {
      builder.addText(`寄件人: ${data.sender}`, 5, 60, { font: 2 })
    }
    
    return builder.build()
  }

  /**
   * 创建样品标签
   * @param {Object} data - 样品数据
   * @param {Object} options - 选项
   */
  static createSampleLabel(data, options = {}) {
    const defaults = {
      width: 80,
      height: 50,
      dpi: 203
    }
    
    const opts = { ...defaults, ...options }
    const builder = new CPCLBuilder()
    
    builder.setPage(opts.width, opts.height, opts.dpi)
    
    // 样品编号
    if (data.sampleId) {
      builder.addText(`样品编号: ${data.sampleId}`, 3, 3, { font: 2, bold: true })
    }
    
    // 采样日期
    if (data.sampleDate) {
      builder.addText(`采样日期: ${data.sampleDate}`, 3, 13, { font: 1 })
    }
    
    // 采样点
    if (data.samplePoint) {
      builder.addText(`采样点: ${data.samplePoint}`, 3, 21, { font: 1 })
    }
    
    // 检测项目
    if (data.testItems) {
      builder.addText(`检测项目: ${data.testItems}`, 3, 29, { font: 1 })
    }
    
    // 二维码
    if (data.qrCode) {
      builder.addQRCode(data.qrCode, 55, 10, { size: 4 })
    }
    
    return builder.build()
  }
}

/**
 * CPCL打印工具函数
 */

/**
 * 发送CPCL指令到打印机
 * @param {string} cpclCommands - CPCL指令
 * @param {Object} options - 打印选项
 */
export async function sendToPrinter(cpclCommands, options = {}) {
  const defaults = {
    printerName: 'default',
    method: 'websocket', // websocket, http, serial
    host: 'localhost',
    port: 9100
  }
  
  const opts = { ...defaults, ...options }
  
  try {
    if (opts.method === 'websocket') {
      await sendViaWebSocket(cpclCommands, opts)
    } else if (opts.method === 'http') {
      await sendViaHttp(cpclCommands, opts)
    } else if (opts.method === 'serial') {
      await sendViaSerial(cpclCommands, opts)
    }
  } catch (error) {
    console.error('发送CPCL指令失败:', error)
    throw error
  }
}

/**
 * 通过WebSocket发送指令
 */
async function sendViaWebSocket(commands, options) {
  return new Promise((resolve, reject) => {
    const ws = new WebSocket(`ws://${options.host}:${options.port}`)
    
    ws.onopen = () => {
      ws.send(commands)
    }
    
    ws.onmessage = (event) => {
      console.log('打印机响应:', event.data)
      ws.close()
      resolve(event.data)
    }
    
    ws.onerror = (error) => {
      reject(error)
    }
    
    ws.onclose = () => {
      resolve('打印任务已发送')
    }
  })
}

/**
 * 通过HTTP发送指令
 */
async function sendViaHttp(commands, options) {
  const response = await fetch(`http://${options.host}:${options.port}/print`, {
    method: 'POST',
    headers: {
      'Content-Type': 'text/plain'
    },
    body: commands
  })
  
  if (!response.ok) {
    throw new Error(`HTTP错误: ${response.status}`)
  }
  
  return await response.text()
}

/**
 * 通过串口发送指令（需要Web Serial API支持）
 */
async function sendViaSerial(commands, options) {
  if (!navigator.serial) {
    throw new Error('浏览器不支持Web Serial API')
  }
  
  const port = await navigator.serial.requestPort()
  await port.open({ baudRate: 9600 })
  
  const writer = port.writable.getWriter()
  const encoder = new TextEncoder()
  
  await writer.write(encoder.encode(commands))
  writer.releaseLock()
  
  await port.close()
  return '打印任务已发送'
}
