<template>
  <div class="cpcl-example">
    <el-row :gutter="20">
      <!-- 左侧：标签类型选择和数据输入 -->
      <el-col :span="12">
        <el-card class="input-card">
          <template #header>
            <span>标签配置</span>
          </template>
          
          <!-- 标签类型选择 -->
          <el-form-item label="标签类型">
            <el-radio-group v-model="selectedLabelType" @change="handleLabelTypeChange">
              <el-radio label="product">产品标签</el-radio>
              <el-radio label="address">地址标签</el-radio>
              <el-radio label="sample">样品标签</el-radio>
              <el-radio label="custom">自定义</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <!-- 产品标签数据输入 -->
          <div v-if="selectedLabelType === 'product'">
            <h4>产品信息</h4>
            <el-form :model="productData" label-width="80px">
              <el-form-item label="产品名称">
                <el-input v-model="productData.name" placeholder="请输入产品名称" />
              </el-form-item>
              <el-form-item label="产品编号">
                <el-input v-model="productData.code" placeholder="请输入产品编号" />
              </el-form-item>
              <el-form-item label="产品价格">
                <el-input-number v-model="productData.price" :precision="2" :min="0" />
              </el-form-item>
              <el-form-item label="条形码">
                <el-input v-model="productData.barcode" placeholder="请输入条形码" />
              </el-form-item>
            </el-form>
          </div>
          
          <!-- 地址标签数据输入 -->
          <div v-if="selectedLabelType === 'address'">
            <h4>地址信息</h4>
            <el-form :model="addressData" label-width="80px">
              <el-form-item label="收件人">
                <el-input v-model="addressData.recipient" placeholder="请输入收件人姓名" />
              </el-form-item>
              <el-form-item label="联系电话">
                <el-input v-model="addressData.phone" placeholder="请输入联系电话" />
              </el-form-item>
              <el-form-item label="收货地址">
                <el-input 
                  v-model="addressData.address" 
                  type="textarea" 
                  :rows="3"
                  placeholder="请输入详细地址" 
                />
              </el-form-item>
              <el-form-item label="邮政编码">
                <el-input v-model="addressData.zipCode" placeholder="请输入邮政编码" />
              </el-form-item>
              <el-form-item label="寄件人">
                <el-input v-model="addressData.sender" placeholder="请输入寄件人姓名" />
              </el-form-item>
            </el-form>
          </div>
          
          <!-- 样品标签数据输入 -->
          <div v-if="selectedLabelType === 'sample'">
            <h4>样品信息</h4>
            <el-form :model="sampleData" label-width="80px">
              <el-form-item label="样品编号">
                <el-input v-model="sampleData.sampleId" placeholder="请输入样品编号" />
              </el-form-item>
              <el-form-item label="采样日期">
                <el-date-picker
                  v-model="sampleData.sampleDate"
                  type="date"
                  placeholder="选择采样日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                />
              </el-form-item>
              <el-form-item label="采样点">
                <el-input v-model="sampleData.samplePoint" placeholder="请输入采样点" />
              </el-form-item>
              <el-form-item label="检测项目">
                <el-input v-model="sampleData.testItems" placeholder="请输入检测项目" />
              </el-form-item>
              <el-form-item label="二维码数据">
                <el-input v-model="sampleData.qrCode" placeholder="请输入二维码数据" />
              </el-form-item>
            </el-form>
          </div>
          
          <!-- 自定义CPCL指令输入 -->
          <div v-if="selectedLabelType === 'custom'">
            <h4>自定义CPCL指令</h4>
            <el-input
              v-model="customCPCL"
              type="textarea"
              :rows="10"
              placeholder="请输入CPCL指令，例如：
! 0 203 203 100 1
TEXT 4 0 50 50 Hello World
PRINT"
            />
          </div>
          
          <!-- 快速填充按钮 -->
          <div class="quick-fill-buttons">
            <el-space>
              <el-button size="small" @click="fillSampleData">填充示例数据</el-button>
              <el-button size="small" @click="clearData">清空数据</el-button>
              <el-button size="small" @click="generateQRCode">生成二维码</el-button>
            </el-space>
          </div>
        </el-card>
      </el-col>
      
      <!-- 右侧：CPCL打印组件 -->
      <el-col :span="12">
        <CPCLPrinter
          :label-type="selectedLabelType"
          :label-data="currentLabelData"
          :custom-cpcl="customCPCL"
          @print-success="handlePrintSuccess"
          @print-error="handlePrintError"
          @settings-changed="handleSettingsChanged"
          @clear-content="handleClearContent"
        />
      </el-col>
    </el-row>
    
    <!-- 底部：CPCL指令说明 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <template #header>
            <span>CPCL指令说明</span>
          </template>
          
          <el-collapse>
            <el-collapse-item title="基础指令" name="basic">
              <div class="instruction-content">
                <h4>页面设置</h4>
                <code>! 0 203 203 100 1</code> - 设置页面参数（偏移量 DPI_X DPI_Y 高度 数量）<br>
                <code>PAGE-WIDTH 400</code> - 设置页面宽度<br><br>
                
                <h4>文本指令</h4>
                <code>TEXT 字体 旋转角度 X坐标 Y坐标 文本内容</code><br>
                字体大小：0-7（0最小，7最大）<br>
                旋转角度：0, 90, 180, 270<br><br>
                
                <h4>打印指令</h4>
                <code>PRINT</code> - 打印一份<br>
                <code>PRINT 3</code> - 打印3份
              </div>
            </el-collapse-item>
            
            <el-collapse-item title="条形码指令" name="barcode">
              <div class="instruction-content">
                <h4>一维条形码</h4>
                <code>BARCODE 类型 宽度倍数 旋转角度 高度 X坐标 Y坐标 数据</code><br>
                常用类型：CODE128, CODE39, EAN13, EAN8<br><br>
                
                <h4>二维码</h4>
                <code>BARCODE QR X坐标 Y坐标 M 2 U 大小</code><br>
                <code>MA,二维码数据</code><br>
                <code>ENDQR</code>
              </div>
            </el-collapse-item>
            
            <el-collapse-item title="图形指令" name="graphics">
              <div class="instruction-content">
                <h4>线条</h4>
                <code>LINE X1 Y1 X2 Y2 宽度</code><br><br>
                
                <h4>矩形</h4>
                <code>BOX X1 Y1 X2 Y2 线宽</code> - 空心矩形<br>
                <code>BOX X1 Y1 X2 Y2 线宽 填充</code> - 实心矩形<br><br>
                
                <h4>图片</h4>
                <code>BITMAP X Y 宽度 高度 抖动算法 图片数据</code>
              </div>
            </el-collapse-item>
            
            <el-collapse-item title="格式化指令" name="format">
              <div class="instruction-content">
                <h4>字体样式</h4>
                <code>SETBOLD 1</code> - 开启粗体<br>
                <code>SETBOLD 0</code> - 关闭粗体<br>
                <code>UNDERLINE ON</code> - 开启下划线<br>
                <code>UNDERLINE OFF</code> - 关闭下划线<br><br>
                
                <h4>对齐方式</h4>
                <code>LEFT</code> - 左对齐<br>
                <code>CENTER</code> - 居中对齐<br>
                <code>RIGHT</code> - 右对齐
              </div>
            </el-collapse-item>
          </el-collapse>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import CPCLPrinter from '@/components/CPCLPrinter/index.vue'

// 响应式数据
const selectedLabelType = ref('product')
const customCPCL = ref('')

// 各种标签数据
const productData = ref({
  name: '',
  code: '',
  price: 0,
  barcode: ''
})

const addressData = ref({
  recipient: '',
  phone: '',
  address: '',
  zipCode: '',
  sender: ''
})

const sampleData = ref({
  sampleId: '',
  sampleDate: '',
  samplePoint: '',
  testItems: '',
  qrCode: ''
})

// 计算属性
const currentLabelData = computed(() => {
  switch (selectedLabelType.value) {
    case 'product':
      return productData.value
    case 'address':
      return addressData.value
    case 'sample':
      return sampleData.value
    default:
      return {}
  }
})

// 方法
const handleLabelTypeChange = (type) => {
  console.log('标签类型切换:', type)
}

const fillSampleData = () => {
  switch (selectedLabelType.value) {
    case 'product':
      productData.value = {
        name: 'iPhone 15 Pro',
        code: 'IP15P-001',
        price: 8999.00,
        barcode: '1234567890123'
      }
      break
    case 'address':
      addressData.value = {
        recipient: '张三',
        phone: '13800138000',
        address: '北京市朝阳区建国路88号SOHO现代城A座1001室',
        zipCode: '100022',
        sender: '李四'
      }
      break
    case 'sample':
      sampleData.value = {
        sampleId: 'S2024010001',
        sampleDate: '2024-01-15',
        samplePoint: 'A区1号采样点',
        testItems: 'pH值,浊度,余氯',
        qrCode: 'S2024010001|2024-01-15|A区1号采样点'
      }
      break
    case 'custom':
      customCPCL.value = `! 0 203 203 200 1
PAGE-WIDTH 400
TEXT 4 0 50 50 Hello CPCL!
TEXT 2 0 50 80 This is a sample label
LINE 50 100 350 100 2
BARCODE CODE128 2 0 50 50 120 1234567890
PRINT`
      break
  }
  
  ElMessage.success('示例数据已填充')
}

const clearData = () => {
  switch (selectedLabelType.value) {
    case 'product':
      productData.value = {
        name: '',
        code: '',
        price: 0,
        barcode: ''
      }
      break
    case 'address':
      addressData.value = {
        recipient: '',
        phone: '',
        address: '',
        zipCode: '',
        sender: ''
      }
      break
    case 'sample':
      sampleData.value = {
        sampleId: '',
        sampleDate: '',
        samplePoint: '',
        testItems: '',
        qrCode: ''
      }
      break
    case 'custom':
      customCPCL.value = ''
      break
  }
  
  ElMessage.success('数据已清空')
}

const generateQRCode = () => {
  if (selectedLabelType.value === 'sample') {
    const data = sampleData.value
    const qrData = `${data.sampleId}|${data.sampleDate}|${data.samplePoint}|${data.testItems}`
    sampleData.value.qrCode = qrData
    ElMessage.success('二维码数据已生成')
  } else if (selectedLabelType.value === 'product') {
    const data = productData.value
    const qrData = `${data.code}|${data.name}|${data.price}`
    productData.value.barcode = data.code || '1234567890'
    ElMessage.success('条形码数据已生成')
  } else {
    ElMessage.warning('当前标签类型不支持自动生成二维码')
  }
}

const handlePrintSuccess = (result) => {
  console.log('打印成功:', result)
  ElMessage.success('标签打印成功')
}

const handlePrintError = (error) => {
  console.error('打印失败:', error)
  ElMessage.error('标签打印失败: ' + error.message)
}

const handleSettingsChanged = (settings) => {
  console.log('打印设置已更改:', settings)
}

const handleClearContent = () => {
  clearData()
}
</script>

<style scoped>
.cpcl-example {
  padding: 20px;
}

.input-card {
  height: fit-content;
}

.quick-fill-buttons {
  margin-top: 20px;
  text-align: center;
}

.instruction-content {
  font-family: monospace;
  line-height: 1.6;
}

.instruction-content h4 {
  color: #409eff;
  margin: 10px 0 5px 0;
}

.instruction-content code {
  background-color: #f5f5f5;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
}
</style>
